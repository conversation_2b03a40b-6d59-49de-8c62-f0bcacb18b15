# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理环境变量、API端点、缓存设置等配置信息
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    # 华为云IoT配置
    HUAWEI_AK = os.getenv("HUAWEI_AK")
    HUAWEI_SK = os.getenv("HUAWEI_SK")
    HUAWEI_ENDPOINT = os.getenv("HUAWEI_ENDPOINT", "5930c00e73.st1.iotda-app.cn-north-4.myhuaweicloud.com")
    HUAWEI_REGION = os.getenv("HUAWEI_REGION", "cn-north-4")
    
    # 主设备配置 - 包含所有3个节点的数据
    MAIN_DEVICE_ID = "685a734ad582f2001834985f_loong_1"
    
    # 节点配置
    NODES_COUNT = 3  # 支持的节点数量
    
    # API配置
    API_HOST = os.getenv("API_HOST", "127.0.0.1")
    API_PORT = int(os.getenv("API_PORT", 5000))
    
    # 缓存配置
    CACHE_TIMEOUT = int(os.getenv("CACHE_TIMEOUT", 30))  # 30秒缓存
    
    # CORS配置
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://127.0.0.1:8000,http://localhost:8000").split(",")

    # DeepSeek AI配置
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")  # 用户需要填写API Key
    DEEPSEEK_API_URL = os.getenv("DEEPSEEK_API_URL", "https://api.deepseek.com/v1/chat/completions")
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")

    @classmethod
    def validate(cls):
        """验证必要的配置项"""
        if not cls.HUAWEI_AK or not cls.HUAWEI_SK:
            raise ValueError("请先设置环境变量 HUAWEI_AK 和 HUAWEI_SK")
        return True

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

# 根据环境变量选择配置
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.getenv('FLASK_ENV', 'default')
    return config_map.get(env, DevelopmentConfig)
