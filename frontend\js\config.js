// 前端配置管理
export const CONFIG = {
  // API配置
  API_BASE_URL: 'http://127.0.0.1:5000',
  API_ENDPOINTS: {
    SENSORS: '/api/sensors'
  },
  
  // 更新间隔配置
  UPDATE_INTERVAL: 10000, // 10秒
  RETRY_INTERVAL: 5000,   // 5秒重试
  MAX_RETRIES: 3,
  
  // 传感器配置
  SENSORS: {
    temperature: {
      name: '温度',
      unit: '°C',
      icon: '🌡️',
      color: 'var(--sensor-temperature)',
      min: 0,
      max: 50
    },
    humidity: {
      name: '湿度', 
      unit: '%',
      icon: '💧',
      color: 'var(--sensor-humidity)',
      min: 0,
      max: 100
    },
    light: {
      name: '光照',
      unit: 'lux',
      icon: '☀️',
      color: 'var(--sensor-light)',
      min: 0,
      max: 1000
    },
    smoke: {
      name: '烟雾',
      unit: 'ppm',
      icon: '💨',
      color: 'var(--sensor-smoke)',
      min: 0,
      max: 500
    }
  },
  
  // 节点配置
  NODES_COUNT: 3,
  
  // 调试模式
  DEBUG: true
};

// 日志工具
export const Logger = {
  log: (message, ...args) => {
    if (CONFIG.DEBUG) {
      console.log(`[IoT] ${message}`, ...args);
    }
  },
  
  error: (message, ...args) => {
    console.error(`[IoT Error] ${message}`, ...args);
  },
  
  warn: (message, ...args) => {
    console.warn(`[IoT Warning] ${message}`, ...args);
  }
};
