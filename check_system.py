#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查脚本
用于验证IoT监控系统和AI助手的配置状态
"""

import os
import sys
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'flask',
        'flask_cors', 
        'requests',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_project_structure():
    """检查项目文件结构"""
    print("\n📁 检查项目结构...")
    
    required_files = [
        'app.py',
        'backend/config.py',
        'backend/api/ai_assistant.py',
        'frontend/test.html',
        'requirements.txt'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_env_config():
    """检查环境变量配置"""
    print("\n🔧 检查环境配置...")
    
    # 检查.env文件
    env_file = '.env'
    env_example = '.env.example'
    
    if os.path.exists(env_file):
        print(f"✅ {env_file} 文件存在")
    else:
        print(f"⚠️ {env_file} 文件不存在")
        if os.path.exists(env_example):
            print(f"💡 可以复制 {env_example} 为 {env_file}")
    
    # 检查关键环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # 华为云配置
        huawei_ak = os.getenv('HUAWEI_AK')
        huawei_sk = os.getenv('HUAWEI_SK')
        
        if huawei_ak and huawei_sk:
            print("✅ 华为云IoT配置已设置")
        else:
            print("⚠️ 华为云IoT配置未设置（将使用模拟数据）")
        
        # DeepSeek配置
        deepseek_key = os.getenv('DEEPSEEK_API_KEY')
        
        if deepseek_key:
            print("✅ DeepSeek API Key已配置")
        else:
            print("⚠️ DeepSeek API Key未配置（AI助手功能不可用）")
            
    except Exception as e:
        print(f"❌ 环境配置检查失败: {e}")
        return False
    
    return True

def check_port_availability():
    """检查端口可用性"""
    print("\n🌐 检查端口可用性...")
    
    import socket
    
    def is_port_available(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('127.0.0.1', port))
                return True
            except OSError:
                return False
    
    port = 5000
    if is_port_available(port):
        print(f"✅ 端口 {port} 可用")
        return True
    else:
        print(f"❌ 端口 {port} 被占用")
        print("💡 请关闭占用端口的程序或修改配置使用其他端口")
        return False

def main():
    """主检查函数"""
    print("🔍 IoT监控系统状态检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("项目结构", check_project_structure),
        ("环境配置", check_env_config),
        ("端口可用性", check_port_availability)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查异常: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 50)
    print("📊 检查结果汇总:")
    
    for check_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {check_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n🎯 总体状态: {success_count}/{total_count} 项检查通过")
    
    if success_count == total_count:
        print("🎉 系统状态良好！可以启动服务。")
        print("\n🚀 启动命令:")
        print("   python app.py")
    else:
        print("⚠️ 发现问题，请根据上述提示进行修复。")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n💡 下一步:")
        print("   1. 运行 'python app.py' 启动系统")
        print("   2. 访问 http://127.0.0.1:5000")
        print("   3. 点击右下角🤖按钮使用AI助手")
    
    sys.exit(0 if success else 1)
