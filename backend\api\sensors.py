# -*- coding: utf-8 -*-
"""
传感器数据API路由
提供传感器数据的RESTful API接口
"""

from flask import Blueprint, request
import logging
from ..iot_client import get_iot_client
from ..utils.cache import get_cache
from ..utils.response import api_success, api_error, sensors_response

# 创建蓝图
sensors_bp = Blueprint('sensors', __name__, url_prefix='/api')

# 配置日志
logger = logging.getLogger(__name__)

@sensors_bp.route('/sensors', methods=['GET'])
def get_sensors_data():
    """获取所有节点的传感器数据"""
    try:
        cache = get_cache()
        cache_key = "sensors_data"
        
        # 尝试从缓存获取数据
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info("从缓存返回传感器数据")
            return api_success(sensors_response(cached_data, cache_hit=True)["data"])
        
        # 缓存未命中，从IoT客户端获取数据
        logger.info("缓存未命中，从华为云获取传感器数据")
        iot_client = get_iot_client()
        nodes_data = iot_client.get_all_devices_data()
        
        if not nodes_data:
            return api_error("未能获取到传感器数据", code=503)
        
        # 将数据存入缓存
        cache.set(cache_key, nodes_data)
        logger.info(f"成功获取并缓存{len(nodes_data)}个节点数据")
        
        # 返回响应
        response_data = sensors_response(nodes_data, cache_hit=False)
        return api_success(response_data["data"])
        
    except Exception as e:
        logger.error(f"获取传感器数据失败: {e}")
        return api_error(f"服务器内部错误: {str(e)}", code=500)

@sensors_bp.route('/sensors/<node_id>', methods=['GET'])
def get_node_sensors_data(node_id):
    """获取指定节点的传感器数据"""
    try:
        cache = get_cache()
        cache_key = "sensors_data"
        
        # 尝试从缓存获取数据
        cached_data = cache.get(cache_key)
        if cached_data and node_id in cached_data:
            logger.info(f"从缓存返回节点{node_id}数据")
            return api_success(cached_data[node_id])
        
        # 缓存未命中，从IoT客户端获取数据
        iot_client = get_iot_client()
        nodes_data = iot_client.get_all_devices_data()
        
        if not nodes_data or node_id not in nodes_data:
            return api_error(f"节点{node_id}不存在", code=404)
        
        # 将数据存入缓存
        cache.set(cache_key, nodes_data)
        
        return api_success(nodes_data[node_id])
        
    except Exception as e:
        logger.error(f"获取节点{node_id}数据失败: {e}")
        return api_error(f"服务器内部错误: {str(e)}", code=500)

@sensors_bp.route('/sensors/stats', methods=['GET'])
def get_sensors_stats():
    """获取传感器数据统计信息"""
    try:
        cache = get_cache()
        cache_stats = cache.get_stats()
        
        # 获取当前数据
        cache_key = "sensors_data"
        cached_data = cache.get(cache_key)
        
        stats = {
            "cache": cache_stats,
            "nodes_status": {}
        }
        
        if cached_data:
            for node_id, node_data in cached_data.items():
                stats["nodes_status"][node_id] = {
                    "status": node_data.get("status", "unknown"),
                    "last_update": node_data.get("last_update", ""),
                    "sensors_count": len(node_data.get("sensors", {}))
                }
        
        return api_success(stats)
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return api_error(f"服务器内部错误: {str(e)}", code=500)

@sensors_bp.route('/sensors/cache', methods=['DELETE'])
def clear_sensors_cache():
    """清空传感器数据缓存"""
    try:
        cache = get_cache()
        cache.clear()
        logger.info("传感器数据缓存已清空")
        return api_success(message="缓存已清空")
        
    except Exception as e:
        logger.error(f"清空缓存失败: {e}")
        return api_error(f"服务器内部错误: {str(e)}", code=500)
