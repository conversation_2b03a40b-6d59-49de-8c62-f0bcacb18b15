// 动画控制逻辑
import { CONFIG, Logger } from './config.js';

export class AnimationController {
  constructor() {
    this.animationQueue = [];
    this.isAnimating = false;
    this.observers = new Map();
    
    this.init();
  }

  init() {
    // 设置Intersection Observer用于页面加载动画
    this.setupIntersectionObserver();
    
    // 监听页面加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startPageLoadAnimation());
    } else {
      this.startPageLoadAnimation();
    }
    
    Logger.log('动画控制器初始化完成');
  }

  setupIntersectionObserver() {
    // 创建观察器用于元素进入视口时的动画
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.triggerElementAnimation(entry.target);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });

    this.observers.set('viewport', observer);
  }

  startPageLoadAnimation() {
    Logger.log('开始页面加载动画');
    
    // 头部动画
    const header = document.querySelector('.app-header');
    if (header) {
      this.addAnimation(header, 'slide-in-left', 0);
    }

    // 标题动画
    const title = document.querySelector('.section-title');
    if (title) {
      this.addAnimation(title, 'fade-in-up', 200);
    }

    // 节点卡片依次动画
    const nodeCards = document.querySelectorAll('.node-card');
    nodeCards.forEach((card, index) => {
      this.addAnimation(card, 'fade-in-up', 300 + (index * 150));
    });

    // 底部动画
    const footer = document.querySelector('.app-footer');
    if (footer) {
      this.addAnimation(footer, 'fade-in', 800);
    }
  }

  addAnimation(element, animationClass, delay = 0) {
    if (!element) return;

    // 添加GPU加速
    element.classList.add('gpu-accelerated');
    
    // 设置初始状态
    element.style.opacity = '0';
    element.style.transform = this.getInitialTransform(animationClass);

    setTimeout(() => {
      element.style.opacity = '';
      element.style.transform = '';
      element.classList.add(animationClass);
      
      // 动画完成后清理
      const animationDuration = this.getAnimationDuration(animationClass);
      setTimeout(() => {
        element.classList.remove(animationClass);
        element.classList.remove('gpu-accelerated');
      }, animationDuration);
      
    }, delay);
  }

  getInitialTransform(animationClass) {
    const transformMap = {
      'fade-in-up': 'translateY(30px)',
      'slide-in-left': 'translateX(-30px)',
      'slide-in-right': 'translateX(30px)',
      'fade-in': 'none'
    };
    return transformMap[animationClass] || 'none';
  }

  getAnimationDuration(animationClass) {
    // 返回动画持续时间（毫秒）
    const durationMap = {
      'fade-in': 300,
      'fade-in-up': 300,
      'slide-in-left': 300,
      'slide-in-right': 300,
      'value-change': 400,
      'count-up': 300,
      'bounce': 1000
    };
    return durationMap[animationClass] || 300;
  }

  // 数值变化动画
  animateValueChange(element, oldValue, newValue, config) {
    if (!element || typeof oldValue !== 'number' || typeof newValue !== 'number') {
      return;
    }

    element.classList.add('value-change');
    
    // 数值计数动画
    this.animateCounter(element, oldValue, newValue, config, 500);
    
    // 清理动画类
    setTimeout(() => {
      element.classList.remove('value-change');
    }, 400);
  }

  animateCounter(element, start, end, config, duration = 500) {
    const startTime = performance.now();
    const difference = end - start;

    const updateCounter = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用easeOutQuart缓动函数
      const easedProgress = 1 - Math.pow(1 - progress, 4);
      const currentValue = start + (difference * easedProgress);
      
      // 更新显示值
      element.textContent = this.formatValue(currentValue, config);
      
      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      } else {
        // 确保最终值准确
        element.textContent = this.formatValue(end, config);
      }
    };

    requestAnimationFrame(updateCounter);
  }

  formatValue(value, config) {
    let formattedValue;
    if (value % 1 === 0) {
      formattedValue = Math.round(value).toString();
    } else {
      formattedValue = value.toFixed(1);
    }
    return `${formattedValue}${config.unit}`;
  }

  // 状态变化动画
  animateStatusChange(element, oldStatus, newStatus) {
    if (!element || oldStatus === newStatus) return;

    element.classList.add('gpu-accelerated');
    
    // 添加状态变化动画
    const animationClass = `status-${newStatus}`;
    element.classList.add(animationClass);
    
    setTimeout(() => {
      element.classList.remove(animationClass);
      element.classList.remove('gpu-accelerated');
    }, 300);
  }

  // 进度条动画
  animateProgress(progressElement, targetWidth, duration = 500) {
    if (!progressElement) return;

    progressElement.style.setProperty('--progress-width', `${targetWidth}%`);
    progressElement.classList.add('gpu-accelerated');
    
    // 使用CSS动画
    progressElement.style.animation = `progressFill ${duration}ms var(--ease-out-quart)`;
    
    setTimeout(() => {
      progressElement.style.animation = '';
      progressElement.classList.remove('gpu-accelerated');
    }, duration);
  }

  // 卡片悬停动画增强
  enhanceCardHover(cardElement) {
    if (!cardElement) return;

    let hoverTimeout;
    
    cardElement.addEventListener('mouseenter', () => {
      clearTimeout(hoverTimeout);
      cardElement.classList.add('hover-lift', 'gpu-accelerated');
      
      // 添加微妙的弹跳效果
      const sensors = cardElement.querySelectorAll('.sensor-icon');
      sensors.forEach((sensor, index) => {
        setTimeout(() => {
          sensor.classList.add('bounce');
          setTimeout(() => sensor.classList.remove('bounce'), 1000);
        }, index * 100);
      });
    });

    cardElement.addEventListener('mouseleave', () => {
      hoverTimeout = setTimeout(() => {
        cardElement.classList.remove('hover-lift', 'gpu-accelerated');
      }, 100);
    });
  }

  // 按钮点击动画
  enhanceButtonClick(buttonElement) {
    if (!buttonElement) return;

    buttonElement.addEventListener('click', (e) => {
      // 创建涟漪效果
      const ripple = document.createElement('span');
      const rect = buttonElement.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
      `;
      
      buttonElement.style.position = 'relative';
      buttonElement.style.overflow = 'hidden';
      buttonElement.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  }

  // 清理所有动画
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.animationQueue = [];
    Logger.log('动画控制器已清理');
  }
}

// 添加涟漪动画CSS
const rippleCSS = `
@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}
`;

// 注入涟漪动画样式（仅在浏览器环境中）
function injectRippleCSS() {
  if (typeof document !== 'undefined' && !document.querySelector('#ripple-animation-style')) {
    const style = document.createElement('style');
    style.id = 'ripple-animation-style';
    style.textContent = rippleCSS;
    document.head.appendChild(style);
  }
}

// 延迟注入CSS，确保DOM已加载
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectRippleCSS);
  } else {
    injectRippleCSS();
  }
}

// 导出单例（仅在浏览器环境中创建）
export const animationController = typeof document !== 'undefined' ? new AnimationController() : null;
