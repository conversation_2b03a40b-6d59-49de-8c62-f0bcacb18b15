# IoT传感器监控系统 - 使用说明

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量（可选）
如果要使用真实的华为云IoT数据和AI助手功能，请配置 `.env` 文件：
```env
# 华为云IoT配置
HUAWEI_AK=your_access_key
HUAWEI_SK=your_secret_key
HUAWEI_ENDPOINT=your_iot_endpoint
HUAWEI_REGION=cn-north-4

# DeepSeek AI配置（AI助手功能）
DEEPSEEK_API_KEY=your_deepseek_api_key
```

### 3. 启动系统
```bash
python app.py
```

### 4. 访问界面
打开浏览器访问：**http://127.0.0.1:5000**

## 📊 功能特性

### ✅ 实时数据监控
- **3个IoT节点**：显示节点在线状态
- **4种传感器**：温度、湿度、光照、烟雾浓度
- **实时更新**：每10秒自动刷新数据
- **状态指示**：连接状态实时显示

### ✅ 可视化曲线图
- **历史趋势**：显示传感器数据变化曲线
- **多节点对比**：同一图表显示3个节点数据
- **时间范围**：支持10分钟、30分钟、1小时视图
- **颜色区分**：
  - 🔵 节点1：蓝色
  - 🟢 节点2：绿色  
  - 🟠 节点3：橙色

### ✅ AI数据助手 🤖
- **智能对话**：基于DeepSeek大模型的自然语言交互
- **数据分析**：自动分析传感器数据，提供专业洞察
- **快捷操作**：一键分析、趋势预测、异常检测
- **使用方法**：点击右下角🤖按钮打开AI助手面板

### ✅ 响应式设计
- **多设备适配**：手机、平板、桌面完美显示
- **苹果设计风格**：现代化UI，圆角阴影
- **平滑动画**：页面加载和数据更新动效

## 🔧 技术架构

### 后端
- **Flask**：Web框架
- **华为云IoT SDK**：数据获取
- **CORS支持**：跨域访问

### 前端
- **纯HTML/CSS/JS**：无框架依赖
- **Canvas图表**：高性能数据可视化
- **实时WebAPI**：数据自动更新

## 📁 项目文件

```
├── app.py              # 主应用（启动这个文件）
├── backend/            # 后端服务
├── frontend/           # 前端页面
│   └── test.html      # 主监控页面
├── requirements.txt    # Python依赖
└── README.md          # 详细文档
```

## 🎯 使用场景

- **IoT设备监控**：实时查看传感器状态
- **数据分析**：观察传感器数据变化趋势
- **系统演示**：展示IoT数据可视化效果
- **开发测试**：验证IoT数据接口功能

## 💡 注意事项

1. **数据源**：系统会自动尝试连接华为云IoT，失败时使用模拟数据
2. **端口占用**：确保5000端口未被其他程序占用
3. **浏览器兼容**：推荐使用Chrome、Firefox、Safari等现代浏览器
4. **网络连接**：使用华为云数据时需要网络连接

## 🛠️ 故障排除

### 服务无法启动
```bash
# 检查端口占用
netstat -ano | findstr :5000

# 安装依赖
pip install -r requirements.txt
```

### 页面无法访问
- 确认服务器已启动
- 检查防火墙设置
- 尝试使用 localhost:5000

### 数据不更新
- 查看浏览器控制台错误
- 检查网络连接
- 验证API接口响应

### AI助手无法使用
- 检查DEEPSEEK_API_KEY是否正确配置
- 验证网络能否访问DeepSeek API
- 查看浏览器控制台的错误信息

---

**🎉 享受现代化的IoT数据监控体验！**
