# -*- coding: utf-8 -*-
"""
华为云IoT客户端封装模块
重构自src/test.py，提供可复用的IoT设备数据获取功能
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from huaweicloudsdkcore.auth.credentials import BasicCredentials
from huaweicloudsdkcore.auth.credentials import DerivedCredentials
from huaweicloudsdkcore.region.region import Region as coreRegion
from huaweicloudsdkcore.exceptions import exceptions
from huaweicloudsdkiotda.v5 import *
from .config import get_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HuaweiIoTClient:
    """华为云IoT客户端封装类"""
    
    def __init__(self, config=None):
        """初始化IoT客户端"""
        self.config = config or get_config()
        self.config.validate()
        self.client = self._create_client()
    
    def _create_client(self) -> IoTDAClient:
        """创建华为云IoT客户端"""
        try:
            credentials = BasicCredentials(
                self.config.HUAWEI_AK, 
                self.config.HUAWEI_SK
            ).with_derived_predicate(DerivedCredentials.get_default_derived_predicate())
            
            client = IoTDAClient.new_builder() \
                .with_credentials(credentials) \
                .with_region(coreRegion(
                    id=self.config.HUAWEI_REGION, 
                    endpoint=self.config.HUAWEI_ENDPOINT
                )) \
                .build()
            
            logger.info("华为云IoT客户端初始化成功")
            return client
            
        except Exception as e:
            logger.error(f"华为云IoT客户端初始化失败: {e}")
            raise
    
    def get_device_shadow(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取单个设备的影子数据"""
        try:
            request = ShowDeviceShadowRequest()
            request.device_id = device_id
            response = self.client.show_device_shadow(request)
            
            # 转换响应为字典格式
            if hasattr(response, 'to_dict'):
                return response.to_dict()
            else:
                # 如果没有to_dict方法，尝试转换为字符串再解析
                response_str = str(response)
                return {"raw_response": response_str}
                
        except exceptions.ClientRequestException as e:
            logger.error(f"获取设备 {device_id} 影子数据失败: {e.error_msg}")
            return None
        except Exception as e:
            logger.error(f"获取设备 {device_id} 影子数据异常: {e}")
            return None
    
    def get_all_devices_data(self) -> Dict[str, Any]:
        """获取所有节点的传感器数据"""
        devices_data = {}

        # 获取主设备的数据，因为它包含了所有3个节点的信息
        main_device_id = self.config.MAIN_DEVICE_ID
        shadow_data = self.get_device_shadow(main_device_id)

        if shadow_data:
            logger.info(f"成功获取主设备数据: {main_device_id}")
            # 从主设备数据中提取3个节点的传感器数据
            nodes_sensors = self._extract_all_nodes_data(shadow_data)

            for i in range(1, self.config.NODES_COUNT + 1):  # 节点1, 2, 3
                node_key = f"node_{i}"
                devices_data[node_key] = {
                    "device_id": f"{main_device_id}_node_{i}",
                    "node_id": i,
                    "sensors": nodes_sensors.get(f"node_{i}", {}),
                    "status": "online" if any(nodes_sensors.get(f"node_{i}", {}).values()) else "inactive",
                    "last_update": self._get_last_update_time(shadow_data)
                }
        else:
            logger.warning(f"主设备数据获取失败，使用模拟数据: {main_device_id}")
            # 如果获取失败，为3个节点提供模拟数据
            for i in range(1, self.config.NODES_COUNT + 1):
                devices_data[f"node_{i}"] = self._get_mock_data(f"mock_node_{i}", i)

        return devices_data
    
    def _extract_all_nodes_data(self, shadow_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """从影子数据中提取所有节点的传感器数据"""
        nodes_data = {
            "node_1": {"temperature": None, "humidity": None, "light": None, "smoke": None},
            "node_2": {"temperature": None, "humidity": None, "light": None, "smoke": None},
            "node_3": {"temperature": None, "humidity": None, "light": None, "smoke": None}
        }

        try:
            # 解析影子数据结构
            if "shadow" in shadow_data and isinstance(shadow_data["shadow"], list):
                for shadow_item in shadow_data["shadow"]:
                    if "reported" in shadow_item and "properties" in shadow_item["reported"]:
                        properties = shadow_item["reported"]["properties"]

                        # 提取所有节点的数据
                        for i in range(1, 4):  # Node_1, Node_2, Node_3
                            node_key = f"node_{i}"
                            nodes_data[node_key] = {
                                "temperature": properties.get(f"Node_{i}_Temperature", 0),
                                "humidity": properties.get(f"Node_{i}_Humidity", 0),
                                "light": properties.get(f"Node_{i}_Light", 0),
                                "smoke": properties.get(f"Node_{i}_Smog", 0)
                            }

        except Exception as e:
            logger.warning(f"解析节点数据失败: {e}")

        return nodes_data

    def _get_last_update_time(self, shadow_data: Dict[str, Any]) -> str:
        """获取最后更新时间"""
        try:
            if "shadow" in shadow_data and isinstance(shadow_data["shadow"], list):
                for shadow_item in shadow_data["shadow"]:
                    if "reported" in shadow_item and "event_time" in shadow_item["reported"]:
                        return shadow_item["reported"]["event_time"]
        except Exception:
            pass
        return ""
    
    def _get_mock_data(self, device_id: str, node_num: int) -> Dict[str, Any]:
        """生成模拟数据（用于开发和测试）"""
        import random

        # 为不同节点生成不同范围的数据，使数据更真实
        base_temp = 20 + (node_num - 1) * 3  # 节点1:20°C, 节点2:23°C, 节点3:26°C
        base_humidity = 50 + (node_num - 1) * 5  # 不同湿度基准

        return {
            "device_id": device_id,
            "shadow": {"mock": True},
            "sensors": {
                "temperature": round(base_temp + random.uniform(-3, 8), 1),
                "humidity": round(base_humidity + random.uniform(-10, 20), 1),
                "light": round(300 + random.uniform(0, 700), 0),
                "smoke": round(random.uniform(0, 50), 1)  # 正常情况下烟雾浓度较低
            },
            "status": "mock"
        }

# 全局客户端实例
_iot_client = None

def get_iot_client() -> HuaweiIoTClient:
    """获取IoT客户端单例"""
    global _iot_client
    if _iot_client is None:
        _iot_client = HuaweiIoTClient()
    return _iot_client
