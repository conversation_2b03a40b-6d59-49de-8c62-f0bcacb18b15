# -*- coding: utf-8 -*-
"""
Flask应用主入口
IoT传感器数据API服务
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
from datetime import datetime

# 导入配置和模块
from .config import get_config
from .api.sensors import sensors_bp
from .utils.response import api_success, api_error

def create_app():
    """创建Flask应用实例"""
    app = Flask(__name__)
    
    # 加载配置
    config = get_config()
    app.config.from_object(config)
    
    # 配置CORS
    CORS(app, origins=config.CORS_ORIGINS, supports_credentials=True)
    
    # 配置日志
    setup_logging(app, config)
    
    # 注册蓝图
    app.register_blueprint(sensors_bp)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册基础路由
    register_basic_routes(app)
    
    return app

def setup_logging(app, config):
    """配置日志系统"""
    if config.DEBUG:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        logging.basicConfig(
            level=logging.WARNING,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    app.logger.info("日志系统配置完成")

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found(error):
        return api_error("接口不存在", code=404)
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return api_error("请求方法不允许", code=405)
    
    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f"服务器内部错误: {error}")
        return api_error("服务器内部错误", code=500)
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        app.logger.error(f"未处理的异常: {error}")
        return api_error("服务器异常", code=500)

def register_basic_routes(app):
    """注册基础路由"""
    
    @app.route('/api/info')
    def api_root():
        """API信息接口"""
        return api_success({
            "name": "IoT传感器数据API",
            "version": "1.0.0",
            "description": "华为云IoT传感器数据监控API服务",
            "endpoints": {
                "sensors": "/api/sensors",
                "node_data": "/api/sensors/<node_id>",
                "stats": "/api/sensors/stats",
                "health": "/health"
            }
        })
    
    @app.route('/health')
    def health_check():
        """健康检查接口"""
        try:
            # 检查IoT客户端连接
            from .iot_client import get_iot_client
            iot_client = get_iot_client()
            
            # 检查缓存系统
            from .utils.cache import get_cache
            cache = get_cache()
            cache_stats = cache.get_stats()
            
            return api_success({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "services": {
                    "iot_client": "connected",
                    "cache": "active",
                    "cache_items": cache_stats["total_items"]
                }
            })
            
        except Exception as e:
            app.logger.error(f"健康检查失败: {e}")
            return api_error("服务不健康", code=503, data={"error": str(e)})
    
    @app.before_request
    def log_request():
        """请求日志记录"""
        if app.config.get('DEBUG'):
            app.logger.info(f"{request.method} {request.path} - {request.remote_addr}")
    
    @app.after_request
    def after_request(response):
        """响应后处理"""
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        return response

def main():
    """主函数 - 仅API服务模式"""
    app = create_app()
    config = get_config()

    print(f"🚀 IoT传感器数据API服务启动")
    print(f"📡 服务地址: http://{config.API_HOST}:{config.API_PORT}")
    print(f"🔧 环境模式: {'开发' if config.DEBUG else '生产'}")
    print(f"📊 API文档: http://{config.API_HOST}:{config.API_PORT}/")
    print(f"💡 提示: 使用 python start.py 启动完整系统")

    app.run(
        host=config.API_HOST,
        port=config.API_PORT,
        debug=config.DEBUG,
        threaded=True
    )

if __name__ == '__main__':
    main()
