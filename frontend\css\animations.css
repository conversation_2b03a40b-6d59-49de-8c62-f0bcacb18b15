/* 动画效果 - 苹果设计风格 */

/* 页面加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 卡片悬停动画 */
@keyframes cardHover {
  from {
    transform: translateY(0);
    box-shadow: var(--shadow-card);
  }
  to {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
  }
}

@keyframes cardLeave {
  from {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
  }
  to {
    transform: translateY(0);
    box-shadow: var(--shadow-card);
  }
}

/* 数值变化动画 */
@keyframes valueChange {
  0% {
    transform: scale(1);
    color: var(--text-primary);
  }
  50% {
    transform: scale(1.1);
    color: var(--primary-color);
  }
  100% {
    transform: scale(1);
    color: var(--text-primary);
  }
}

@keyframes countUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 状态颜色渐变动画 */
@keyframes statusOnline {
  from { background-color: var(--status-offline); }
  to { background-color: var(--status-online); }
}

@keyframes statusOffline {
  from { background-color: var(--status-online); }
  to { background-color: var(--status-offline); }
}

@keyframes statusWarning {
  from { background-color: var(--status-online); }
  to { background-color: var(--status-warning); }
}

/* 进度条动画 */
@keyframes progressFill {
  from { width: 0%; }
  to { width: var(--progress-width); }
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 旋转动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 应用动画类 */
.fade-in {
  animation: fadeIn var(--transition-normal) var(--ease-out-quart);
}

.fade-in-up {
  animation: fadeInUp var(--transition-normal) var(--ease-out-quart);
}

.slide-in-left {
  animation: slideInLeft var(--transition-normal) var(--ease-out-quart);
}

.slide-in-right {
  animation: slideInRight var(--transition-normal) var(--ease-out-quart);
}

.value-change {
  animation: valueChange 0.4s var(--ease-out-quart);
}

.count-up {
  animation: countUp 0.3s var(--ease-out-quart);
}

.pulse {
  animation: pulse 2s infinite var(--ease-in-out-quart);
}

.bounce {
  animation: bounce 1s var(--ease-out-quart);
}

.spin {
  animation: spin 1s linear infinite;
}

/* 延迟动画 */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* 交互动画 */
.hover-lift {
  transition: all var(--transition-normal) var(--ease-out-quart);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

.hover-scale {
  transition: transform var(--transition-fast) var(--ease-out-quart);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.click-scale {
  transition: transform var(--transition-fast) var(--ease-out-quart);
}

.click-scale:active {
  transform: scale(0.98);
}

/* 性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .fade-in-up,
  .slide-in-left,
  .slide-in-right,
  .value-change,
  .count-up,
  .pulse,
  .bounce,
  .spin,
  .hover-lift,
  .hover-scale,
  .click-scale {
    animation: none !important;
    transition: none !important;
  }
}
