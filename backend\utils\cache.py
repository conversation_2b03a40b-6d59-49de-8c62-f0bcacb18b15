# -*- coding: utf-8 -*-
"""
数据缓存机制
实现内存缓存，减少华为云API调用频率
"""

import time
import threading
from typing import Any, Optional, Dict
from ..config import get_config

class SimpleCache:
    """简单的内存缓存实现"""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        self.config = get_config()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with self._lock:
            if key not in self._cache:
                return None
            
            cache_item = self._cache[key]
            current_time = time.time()
            
            # 检查是否过期
            if current_time - cache_item['timestamp'] > self.config.CACHE_TIMEOUT:
                del self._cache[key]
                return None
            
            return cache_item['data']
    
    def set(self, key: str, data: Any) -> None:
        """设置缓存数据"""
        with self._lock:
            self._cache[key] = {
                'data': data,
                'timestamp': time.time()
            }
    
    def delete(self, key: str) -> None:
        """删除缓存数据"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            current_time = time.time()
            valid_items = 0
            expired_items = 0
            
            for cache_item in self._cache.values():
                if current_time - cache_item['timestamp'] > self.config.CACHE_TIMEOUT:
                    expired_items += 1
                else:
                    valid_items += 1
            
            return {
                'total_items': len(self._cache),
                'valid_items': valid_items,
                'expired_items': expired_items,
                'cache_timeout': self.config.CACHE_TIMEOUT
            }

# 全局缓存实例
_cache_instance = None

def get_cache() -> SimpleCache:
    """获取缓存实例（单例模式）"""
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = SimpleCache()
    return _cache_instance
