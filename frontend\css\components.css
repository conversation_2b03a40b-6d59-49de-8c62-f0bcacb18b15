/* 组件样式 */

/* 应用容器 */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.app-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-lg) var(--spacing-md);
  box-shadow: var(--shadow-small);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--status-offline);
  transition: background var(--transition-fast);
}

.status-dot.online { background: var(--status-online); }
.status-dot.warning { background: var(--status-warning); }
.status-dot.connecting {
  background: var(--primary-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 主内容区域 */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-md);
  width: 100%;
}

/* 节点网格 */
.nodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

/* 加载占位符 */
.loading-placeholder {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 节点卡片样式 */
.node-card {
  background: var(--bg-card);
  border-radius: var(--radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.node-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.node-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.node-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.node-status.online {
  background: var(--success-color);
  color: white;
}

.node-status.offline {
  background: var(--error-color);
  color: white;
}

.node-status.inactive {
  background: var(--text-tertiary);
  color: var(--text-primary);
}

.node-status.mock {
  background: var(--secondary-color);
  color: white;
}

/* 传感器网格 */
.sensors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

/* 传感器项目 */
.sensor-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-medium);
  transition: all var(--transition-fast);
  position: relative;
}

.sensor-item:hover {
  background: var(--bg-tertiary);
}

.sensor-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-medium);
  background: var(--bg-card);
  box-shadow: var(--shadow-small);
  flex-shrink: 0;
}

.sensor-icon svg {
  width: 20px;
  height: 20px;
}

.sensor-info {
  flex: 1;
  min-width: 0;
}

.sensor-name {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.sensor-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: 1;
}

/* 传感器特定颜色 */
.sensor-item.temperature .sensor-icon {
  background: var(--sensor-temperature);
  color: white;
}

.sensor-item.humidity .sensor-icon {
  background: var(--sensor-humidity);
  color: white;
}

.sensor-item.light .sensor-icon {
  background: var(--sensor-light);
  color: var(--text-primary);
}

.sensor-item.smoke .sensor-icon {
  background: var(--sensor-smoke);
  color: white;
}

/* 进度条样式 */
.sensor-progress {
  margin-top: var(--spacing-xs);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--border-light);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  transition: width var(--transition-normal);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 节点底部信息 */
.node-footer {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.last-update {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.update-label {
  font-weight: var(--font-weight-medium);
}

.update-time {
  color: var(--text-tertiary);
}

/* 错误状态样式 */
.error-section {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-2xl);
}

.error-card {
  background: var(--bg-card);
  border-radius: var(--radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--error-color);
  max-width: 400px;
  margin: 0 auto;
}

.error-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.error-title {
  color: var(--error-color);
  margin-bottom: var(--spacing-md);
}

.error-message {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-relaxed);
}

.retry-button {
  background: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-medium);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.retry-button:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

/* 交互增强样式 */
.sensor-item {
  cursor: pointer;
  user-select: none;
}

.sensor-item:hover .sensor-icon {
  transform: scale(1.1);
  transition: transform var(--transition-fast) var(--ease-out-quart);
}

.sensor-item:active .sensor-icon {
  transform: scale(0.95);
}

/* 状态指示器动画 */
.node-status {
  transition: all var(--transition-normal) var(--ease-out-quart);
  cursor: pointer;
}

.node-status:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 进度条增强 */
.progress-fill {
  position: relative;
  overflow: hidden;
}

.progress-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 卡片状态指示 */
.node-card.online {
  border-left: 4px solid var(--status-online);
}

.node-card.offline {
  border-left: 4px solid var(--status-offline);
}

.node-card.inactive {
  border-left: 4px solid var(--text-tertiary);
}

.node-card.mock {
  border-left: 4px solid var(--secondary-color);
}

/* 加载状态增强 */
.loading-placeholder {
  position: relative;
}

.loading-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 错误状态动画 */
.error-card {
  animation: errorSlideIn 0.3s var(--ease-out-quart);
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 成功状态动画 */
.success-indicator {
  animation: successPulse 0.6s var(--ease-out-quart);
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .nodes-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .main-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .sensors-grid {
    grid-template-columns: 1fr;
  }

  .node-card {
    padding: var(--spacing-md);
  }
}
