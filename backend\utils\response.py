# -*- coding: utf-8 -*-
"""
统一响应格式模块
提供标准化的API响应格式
"""

from flask import jsonify
from typing import Any, Optional, Dict
import time

def success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
    """成功响应格式"""
    return {
        "success": True,
        "code": 200,
        "message": message,
        "data": data,
        "timestamp": int(time.time())
    }

def error_response(message: str = "操作失败", code: int = 500, data: Any = None) -> Dict[str, Any]:
    """错误响应格式"""
    return {
        "success": False,
        "code": code,
        "message": message,
        "data": data,
        "timestamp": int(time.time())
    }

def api_success(data: Any = None, message: str = "操作成功"):
    """返回成功的JSON响应"""
    return jsonify(success_response(data, message))

def api_error(message: str = "操作失败", code: int = 500, data: Any = None):
    """返回错误的JSON响应"""
    response = jsonify(error_response(message, code, data))
    response.status_code = code
    return response

def sensors_response(nodes_data: Dict[str, Any], cache_hit: bool = False) -> Dict[str, Any]:
    """传感器数据专用响应格式"""
    # 统计在线节点数量
    online_count = sum(1 for node in nodes_data.values() if node.get('status') == 'online')
    total_count = len(nodes_data)
    
    return success_response(
        data={
            "nodes": nodes_data,
            "summary": {
                "total_nodes": total_count,
                "online_nodes": online_count,
                "offline_nodes": total_count - online_count,
                "cache_hit": cache_hit
            }
        },
        message=f"成功获取{total_count}个节点数据，{online_count}个在线"
    )
