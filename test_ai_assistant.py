#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI助手功能测试脚本
用于验证DeepSeek API集成是否正常工作
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def test_config():
    """测试配置是否正确"""
    print("🔧 测试配置...")
    
    try:
        from backend.config import get_config
        config = get_config()
        
        print(f"✅ 配置加载成功")
        print(f"   DeepSeek API URL: {config.DEEPSEEK_API_URL}")
        print(f"   DeepSeek Model: {config.DEEPSEEK_MODEL}")
        
        if config.DEEPSEEK_API_KEY:
            print(f"   API Key: {'*' * 20}...{config.DEEPSEEK_API_KEY[-4:]}")
        else:
            print("   ⚠️ API Key未配置")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_ai_assistant():
    """测试AI助手类"""
    print("\n🤖 测试AI助手...")
    
    try:
        from backend.api.ai_assistant import AIAssistant
        
        ai = AIAssistant()
        print("✅ AI助手实例创建成功")
        
        # 测试数据总结功能
        test_data = {
            "nodes": {
                "node_1": {
                    "node_id": "1",
                    "status": "online",
                    "sensors": {
                        "temperature": 25.3,
                        "humidity": 65.2,
                        "light": 320.5,
                        "smoke": 120.8
                    }
                }
            }
        }
        
        summary = ai._summarize_sensor_data(test_data)
        print("✅ 数据总结功能正常")
        print(f"   总结内容: {summary[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ AI助手测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接（需要有效的API Key）"""
    print("\n🌐 测试API连接...")
    
    try:
        from backend.api.ai_assistant import AIAssistant
        from backend.config import get_config
        
        config = get_config()
        
        if not config.DEEPSEEK_API_KEY:
            print("⚠️ 跳过API连接测试（未配置API Key）")
            return True
        
        ai = AIAssistant()
        
        # 简单的测试消息
        test_message = "你好，请简单介绍一下你的功能。"
        
        print(f"   发送测试消息: {test_message}")
        
        # 注意：这会消耗API调用次数
        response = ai._call_deepseek_api(test_message)
        
        print("✅ API连接成功")
        print(f"   响应长度: {len(response)} 字符")
        print(f"   响应预览: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 AI助手功能测试")
    print("=" * 50)
    
    tests = [
        ("配置测试", test_config),
        ("AI助手测试", test_ai_assistant),
        ("API连接测试", test_api_connection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！AI助手功能已就绪。")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接。")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
