# -*- coding: utf-8 -*-
"""
AI助手API模块 - DeepSeek集成
提供智能数据分析和对话功能
"""

import json
import requests
import time
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from backend.config import get_config
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 创建蓝图
ai_bp = Blueprint('ai', __name__)
config = get_config()

class AIAssistant:
    """AI助手类 - 集成DeepSeek API"""
    
    def __init__(self):
        self.api_key = config.DEEPSEEK_API_KEY
        self.api_url = config.DEEPSEEK_API_URL
        self.model = config.DEEPSEEK_MODEL
        self.conversation_history = []  # 对话历史
        
    def analyze_sensor_data(self, sensor_data):
        """分析传感器数据并生成洞察"""
        try:
            # 构建数据分析提示词
            prompt = self._build_analysis_prompt(sensor_data)

            # 调用DeepSeek API
            response = self._call_deepseek_api(prompt)

            return {
                "success": True,
                "analysis": response,
                "source": "AI分析",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            # 如果API调用失败，使用本地分析作为备用
            try:
                local_analysis = self._local_data_analysis(sensor_data)
                return {
                    "success": True,
                    "analysis": local_analysis,
                    "source": "本地分析",
                    "timestamp": datetime.now().isoformat(),
                    "note": f"AI服务暂时不可用({str(e)})，使用本地分析"
                }
            except Exception as local_error:
                return {
                    "success": False,
                    "error": f"AI分析失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
    
    def chat_with_user(self, user_message, sensor_data=None):
        """与用户对话，可选择包含传感器数据上下文"""
        try:
            # 构建对话提示词
            prompt = self._build_chat_prompt(user_message, sensor_data)
            
            # 调用DeepSeek API
            response = self._call_deepseek_api(prompt, include_history=True)
            
            # 更新对话历史
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })
            self.conversation_history.append({
                "role": "assistant", 
                "content": response,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保持对话历史在合理长度
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]
            
            return {
                "success": True,
                "response": response,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _build_analysis_prompt(self, sensor_data):
        """构建数据分析提示词"""
        data_summary = self._summarize_sensor_data(sensor_data)
        
        prompt = f"""
你是一个专业的IoT数据分析师。请分析以下传感器数据并提供专业洞察：

当前传感器数据：
{data_summary}

请从以下角度进行分析：
1. 数据趋势分析
2. 异常值检测
3. 环境状态评估
4. 潜在问题预警
5. 优化建议

请用中文回答，保持专业且易懂。
"""
        return prompt
    
    def _build_chat_prompt(self, user_message, sensor_data=None):
        """构建对话提示词"""
        context = "你是一个专业的IoT传感器监控系统AI助手，专门帮助用户理解和分析传感器数据。"
        
        if sensor_data:
            data_summary = self._summarize_sensor_data(sensor_data)
            context += f"\n\n当前系统数据：\n{data_summary}"
        
        context += f"\n\n用户问题：{user_message}\n\n请用中文回答，保持专业且友好。"
        
        return context
    
    def _summarize_sensor_data(self, sensor_data):
        """总结传感器数据"""
        if not sensor_data or 'nodes' not in sensor_data:
            return "暂无传感器数据"
        
        summary = []
        nodes = sensor_data['nodes']
        
        for node_key, node_data in nodes.items():
            node_id = node_data.get('node_id', node_key.replace('node_', ''))
            status = node_data.get('status', 'unknown')
            sensors = node_data.get('sensors', {})
            
            summary.append(f"节点{node_id} ({status}):")
            summary.append(f"  温度: {sensors.get('temperature', 0)}°C")
            summary.append(f"  湿度: {sensors.get('humidity', 0)}%")
            summary.append(f"  光照: {sensors.get('light', 0)} lux")
            summary.append(f"  烟雾: {sensors.get('smoke', 0)} ppm")
        
        return "\n".join(summary)

    def _local_data_analysis(self, sensor_data):
        """本地数据分析，作为AI分析的备用方案"""
        if not sensor_data or 'nodes' not in sensor_data:
            return "暂无传感器数据可供分析"

        nodes = sensor_data['nodes']
        analysis_parts = []

        # 系统状态概览
        online_nodes = sum(1 for node in nodes.values() if node.get('status') == 'online')
        total_nodes = len(nodes)
        analysis_parts.append(f"📊 系统状态概览:")
        analysis_parts.append(f"   在线节点: {online_nodes}/{total_nodes}")

        # 分析每个在线节点的数据
        for node_key, node_data in nodes.items():
            if node_data.get('status') != 'online':
                continue

            node_id = node_data.get('node_id', node_key.replace('node_', ''))
            sensors = node_data.get('sensors', {})

            analysis_parts.append(f"\n🔍 节点{node_id}数据分析:")

            # 温度分析
            temp = sensors.get('temperature', 0)
            if temp > 30:
                analysis_parts.append(f"   🌡️ 温度: {temp}°C - 偏高，建议检查散热")
            elif temp < 18:
                analysis_parts.append(f"   🌡️ 温度: {temp}°C - 偏低，可能影响设备性能")
            else:
                analysis_parts.append(f"   🌡️ 温度: {temp}°C - 正常范围")

            # 湿度分析
            humidity = sensors.get('humidity', 0)
            if humidity > 70:
                analysis_parts.append(f"   💧 湿度: {humidity}% - 偏高，注意防潮")
            elif humidity < 30:
                analysis_parts.append(f"   💧 湿度: {humidity}% - 偏低，环境较干燥")
            else:
                analysis_parts.append(f"   💧 湿度: {humidity}% - 适宜范围")

            # 光照分析
            light = sensors.get('light', 0)
            if light > 400:
                analysis_parts.append(f"   ☀️ 光照: {light} lux - 光照充足")
            elif light < 100:
                analysis_parts.append(f"   ☀️ 光照: {light} lux - 光照不足")
            else:
                analysis_parts.append(f"   ☀️ 光照: {light} lux - 光照适中")

            # 烟雾分析
            smoke = sensors.get('smoke', 0)
            if smoke > 200:
                analysis_parts.append(f"   💨 烟雾: {smoke} ppm - ⚠️ 浓度偏高，需要关注")
            elif smoke > 150:
                analysis_parts.append(f"   💨 烟雾: {smoke} ppm - 浓度略高，建议通风")
            else:
                analysis_parts.append(f"   💨 烟雾: {smoke} ppm - 正常范围")

        # 离线节点提醒
        offline_nodes = [k for k, v in nodes.items() if v.get('status') != 'online']
        if offline_nodes:
            analysis_parts.append(f"\n⚠️ 离线节点:")
            for node_key in offline_nodes:
                node_id = nodes[node_key].get('node_id', node_key.replace('node_', ''))
                analysis_parts.append(f"   节点{node_id}: {nodes[node_key].get('status', '未知状态')}")

        # 总体建议
        analysis_parts.append(f"\n💡 建议:")
        if online_nodes < total_nodes:
            analysis_parts.append(f"   • 检查离线节点的网络连接和电源状态")
        analysis_parts.append(f"   • 定期检查传感器数据变化趋势")
        analysis_parts.append(f"   • 关注异常数值，及时采取措施")

        return "\n".join(analysis_parts)
    
    def _call_deepseek_api(self, prompt, include_history=False):
        """调用DeepSeek API，带重试机制"""
        if not self.api_key:
            raise Exception("DeepSeek API Key未配置，请在环境变量中设置DEEPSEEK_API_KEY")

        # 创建带重试机制的session
        session = requests.Session()
        retry_strategy = Retry(
            total=3,  # 总重试次数
            backoff_factor=1,  # 重试间隔
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        messages = []

        # 添加系统消息
        messages.append({
            "role": "system",
            "content": "你是一个专业的IoT传感器监控系统AI助手，擅长数据分析和问题解答。请用中文回答，保持专业且友好。"
        })

        # 添加对话历史（如果需要）
        if include_history and self.conversation_history:
            for msg in self.conversation_history[-6:]:  # 只取最近6条，减少token使用
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

        # 添加当前消息
        messages.append({
            "role": "user",
            "content": prompt
        })

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 800,  # 减少token数量，提高响应速度
            "stream": False
        }

        try:
            response = session.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=(10, 60)  # 连接超时10秒，读取超时60秒
            )

            if response.status_code == 429:
                raise Exception("API调用频率过高，请稍后重试")
            elif response.status_code == 401:
                raise Exception("API Key无效，请检查配置")
            elif response.status_code != 200:
                raise Exception(f"API调用失败 (状态码: {response.status_code})")

            result = response.json()

            if 'choices' not in result or not result['choices']:
                raise Exception("API返回数据格式错误")

            return result['choices'][0]['message']['content']

        except requests.exceptions.Timeout:
            raise Exception("网络连接超时，请检查网络连接或稍后重试")
        except requests.exceptions.ConnectionError:
            raise Exception("无法连接到DeepSeek服务器，请检查网络连接")
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        finally:
            session.close()

# 全局AI助手实例
ai_assistant = AIAssistant()

@ai_bp.route('/analyze', methods=['POST'])
def analyze_data():
    """数据分析接口"""
    try:
        data = request.get_json()
        sensor_data = data.get('sensor_data')
        
        if not sensor_data:
            return jsonify({
                "success": False,
                "error": "缺少传感器数据"
            }), 400
        
        result = ai_assistant.analyze_sensor_data(sensor_data)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@ai_bp.route('/chat', methods=['POST'])
def chat():
    """对话接口"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        sensor_data = data.get('sensor_data')
        
        if not user_message:
            return jsonify({
                "success": False,
                "error": "消息不能为空"
            }), 400
        
        result = ai_assistant.chat_with_user(user_message, sensor_data)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@ai_bp.route('/history', methods=['GET'])
def get_history():
    """获取对话历史"""
    try:
        return jsonify({
            "success": True,
            "history": ai_assistant.conversation_history[-10:],  # 返回最近10条
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@ai_bp.route('/clear', methods=['POST'])
def clear_history():
    """清除对话历史"""
    try:
        ai_assistant.conversation_history = []
        return jsonify({
            "success": True,
            "message": "对话历史已清除",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
