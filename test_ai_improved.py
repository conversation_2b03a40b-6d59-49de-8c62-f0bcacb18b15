#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的AI助手功能
包括重试机制和本地分析备用方案
"""

import requests
import json
import time

def test_ai_chat():
    """测试AI对话功能"""
    print("🤖 测试AI对话功能...")
    
    url = "http://127.0.0.1:5000/api/ai/chat"
    data = {
        "message": "你好，请简单介绍一下你的功能",
        "sensor_data": None
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        result = response.json()
        
        if result.get('success'):
            print("✅ AI对话测试成功")
            print(f"   响应: {result['response'][:100]}...")
            return True
        else:
            print(f"❌ AI对话测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ AI对话测试异常: {e}")
        return False

def test_ai_analysis():
    """测试AI数据分析功能"""
    print("\n📊 测试AI数据分析功能...")
    
    # 模拟传感器数据
    test_sensor_data = {
        "nodes": {
            "node_1": {
                "node_id": "1",
                "status": "online",
                "sensors": {
                    "temperature": 28.5,
                    "humidity": 65.2,
                    "light": 320.5,
                    "smoke": 120.8
                }
            },
            "node_2": {
                "node_id": "2", 
                "status": "inactive",
                "sensors": {
                    "temperature": 0,
                    "humidity": 0,
                    "light": 0,
                    "smoke": 0
                }
            }
        }
    }
    
    url = "http://127.0.0.1:5000/api/ai/analyze"
    data = {"sensor_data": test_sensor_data}
    
    try:
        print("   发送分析请求...")
        response = requests.post(url, json=data, timeout=60)
        result = response.json()
        
        if result.get('success'):
            print("✅ AI数据分析测试成功")
            print(f"   分析来源: {result.get('source', '未知')}")
            if result.get('note'):
                print(f"   备注: {result['note']}")
            print(f"   分析结果: {result['analysis'][:200]}...")
            return True
        else:
            print(f"❌ AI数据分析测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ AI数据分析测试异常: {e}")
        return False

def test_local_analysis():
    """直接测试本地分析功能"""
    print("\n🔧 测试本地分析功能...")
    
    try:
        import sys
        import os
        from pathlib import Path
        
        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent.absolute()
        sys.path.insert(0, str(project_root))
        
        from backend.api.ai_assistant import AIAssistant
        
        ai = AIAssistant()
        
        # 测试数据
        test_data = {
            "nodes": {
                "node_1": {
                    "node_id": "1",
                    "status": "online",
                    "sensors": {
                        "temperature": 32.5,  # 偏高
                        "humidity": 75.0,     # 偏高
                        "light": 80.0,        # 偏低
                        "smoke": 180.0        # 略高
                    }
                },
                "node_2": {
                    "node_id": "2",
                    "status": "offline",
                    "sensors": {"temperature": 0, "humidity": 0, "light": 0, "smoke": 0}
                }
            }
        }
        
        analysis = ai._local_data_analysis(test_data)
        
        print("✅ 本地分析功能正常")
        print("   分析结果:")
        for line in analysis.split('\n')[:10]:  # 只显示前10行
            print(f"   {line}")
        if len(analysis.split('\n')) > 10:
            print("   ...")
            
        return True
        
    except Exception as e:
        print(f"❌ 本地分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 改进后的AI助手功能测试")
    print("=" * 50)
    
    tests = [
        ("AI对话功能", test_ai_chat),
        ("AI数据分析", test_ai_analysis),
        ("本地分析备用", test_local_analysis)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count >= 2:  # 至少2个测试通过就算成功
        print("🎉 AI助手功能基本正常！")
        print("\n💡 使用建议:")
        print("   • 如果网络不稳定，系统会自动使用本地分析")
        print("   • 本地分析提供基础的数据评估和建议")
        print("   • AI分析提供更深入的洞察和专业建议")
    else:
        print("⚠️ 多项测试失败，请检查配置和网络连接")
    
    return success_count >= 2

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 现在可以在网页中使用AI助手了！")
        print("   访问: http://127.0.0.1:5000")
        print("   点击右下角🤖按钮开始使用")
    
    exit(0 if success else 1)
