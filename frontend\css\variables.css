/* CSS变量定义 - 苹果设计风格 */
:root {
  /* 主色调 - 苹果蓝 */
  --primary-color: #007AFF;
  --primary-hover: #0056CC;
  --primary-light: #E3F2FD;
  --primary-dark: #004085;

  /* 辅助色彩 */
  --secondary-color: #5856D6;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --error-color: #FF3B30;
  --info-color: #5AC8FA;

  /* 中性色彩 */
  --text-primary: #1C1C1E;
  --text-secondary: #8E8E93;
  --text-tertiary: #C7C7CC;
  --text-inverse: #FFFFFF;

  /* 背景色彩 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F2F2F7;
  --bg-tertiary: #E5E5EA;
  --bg-card: #FFFFFF;
  --bg-overlay: rgba(0, 0, 0, 0.4);

  /* 边框色彩 */
  --border-light: #E5E5EA;
  --border-medium: #C7C7CC;
  --border-dark: #8E8E93;

  /* 阴影 */
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-card: 0 2px 10px rgba(0, 0, 0, 0.08);

  /* 圆角 */
  --radius-small: 6px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-xl: 20px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;

  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;

  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;

  /* 动画曲线 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);

  /* 传感器特定颜色 */
  --sensor-temperature: #FF6B6B;
  --sensor-humidity: #4ECDC4;
  --sensor-light: #FFE66D;
  --sensor-smoke: #95A5A6;

  /* 状态颜色 */
  --status-online: #34C759;
  --status-offline: #FF3B30;
  --status-warning: #FF9500;
  --status-mock: #5856D6;
}
