# coding: utf-8

import os
from huaweicloudsdkcore.auth.credentials import BasicCredentials
from huaweicloudsdkcore.auth.credentials import DerivedCredentials
from huaweicloudsdkcore.region.region import Region as coreRegion
from huaweicloudsdkcore.exceptions import exceptions
from huaweicloudsdkiotda.v5 import *
from dotenv import load_dotenv


load_dotenv(dotenv_path="../.env")  # 如果test.py和.env在同一目录，写".env"

if __name__ == "__main__":
    # The AK and SK used for authentication are hard-coded or stored in plaintext, which has great security risks. It is recommended that the AK and SK be stored in ciphertext in configuration files or environment variables and decrypted during use to ensure security.
    # In this example, AK and SK are stored in environment variables for authentication. Before running this example, set environment variables CLOUD_SDK_AK and CLOUD_SDK_SK in the local environment
    ak = os.getenv("HUAWEI_AK") 
    sk = os.getenv("HUAWEI_SK") 
    if not ak or not sk:
        raise ValueError("请先设置环境变量 HUAWEI_AK 和 HUAWEI_SK")
    # ENDPOINT：请在控制台的"总览"界面的"平台接入地址"中查看“应用侧”的https接入地址，下面创建Client时需要使用自行创建的Region对象，基础版：请选择IoTDAClient中的Region对象 如： IoTDAClient.new_builder().with_region(IoTDARegion.CN_NORTH_4)
    endpoint = "5930c00e73.st1.iotda-app.cn-north-4.myhuaweicloud.com"

    credentials = BasicCredentials(ak, sk).with_derived_predicate(DerivedCredentials.get_default_derived_predicate())

    client = IoTDAClient.new_builder() \
        .with_credentials(credentials) \
        .with_region(coreRegion(id="cn-north-4", endpoint=endpoint)) \
        .build()

    try:
        request = ShowDeviceShadowRequest()
        request.device_id = "685a734ad582f2001834985f_loong_1"
        response = client.show_device_shadow(request)
        print(response)
    except exceptions.ClientRequestException as e:
        print(e.status_code)
        print(e.request_id)
        print(e.error_code)
        print(e.error_msg)

